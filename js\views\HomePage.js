import { mockArticles } from '../services/mockdata.js';

export default async function HomePage() {
  // Obtener los 4 artículos más recientes
  const articles = mockArticles.slice(0, 4);
  return `
    <section class="mb-12">
      <div class="bg-gold-500 rounded-lg p-8 text-center shadow">
        <h1 class="text-4xl font-sans font-bold mb-2">Y la verdad os hará libres</h1>
        <p class="text-lg font-serif mb-4">Un blog de reflexión teológica, académica y espiritual.</p>
      </div>
    </section>
    <section>
      <h2 class="text-2xl font-sans font-semibold mb-4">Artículos recientes</h2>
      <ul class="grid grid-cols-1 md:grid-cols-2 gap-6">
        ${articles.map(article => `
          <li class="bg-white rounded-lg shadow p-6 flex flex-col">
            <a href="/articulo/${article.slug}" data-link class="text-xl font-bold text-navy-900 hover:text-gold-500 mb-2">${article.title}</a>
            <p class="text-gray-700 mb-2">${article.summary}</p>
            <span class="text-xs text-gray-500">${article.createdAt}</span>
          </li>
        `).join('')}
      </ul>
    </section>
  `;
}