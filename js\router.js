// router.js - SPA router using History API
import HomePage from './views/HomePage.js';
import ArticlePage from './views/ArticlePage.js';
import ResourcesPage from './views/ResourcesPage.js';
import TimelinePage from './views/TimelinePage.js';
import AdminPanel from './views/AdminPanel.js';

const routes = [
  { path: /^\/$/, view: HomePage },
  { path: /^\/articulo\/([\w-]+)$/, view: ArticlePage },
  { path: /^\/recursos$/, view: ResourcesPage },
  { path: /^\/linea-temporal$/, view: TimelinePage },
  { path: /^\/admin$/, view: AdminPanel },
];

export async function router() {
  const appRoot = document.getElementById('app-root');
  const path = window.location.pathname;
  for (const route of routes) {
    const match = path.match(route.path);
    if (match) {
      const html = await route.view(...match.slice(1));
      appRoot.innerHTML = html;
      document.dispatchEvent(new CustomEvent('view:loaded', { detail: { route: path } }));
      return;
    }
  }
  appRoot.innerHTML = '<div class="text-center text-gray-500">Página no encontrada.</div>';
}

export function navigate(url) {
  window.history.pushState({}, '', url);
  router();
}

window.addEventListener('popstate', router);

document.addEventListener('click', (e) => {
  const link = e.target.closest('a[data-link]');
  if (link && link.href.startsWith(window.location.origin)) {
    e.preventDefault();
    navigate(link.getAttribute('href'));
  }
});