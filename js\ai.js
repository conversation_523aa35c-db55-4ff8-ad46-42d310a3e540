class AISummaryManager {
    constructor() {
        this.modalTemplate = `
            <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white p-8 rounded-lg shadow-lg max-w-2xl w-full mx-4">
                    <div class="flex justify-between items-start mb-4">
                        <h3 class="text-2xl font-bold text-navy-blue">Resumen del Artículo (IA)</h3>
                        <button id="close-modal-btn" class="text-gray-500 hover:text-gray-700">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div id="summary-content" class="prose prose-sm max-w-none text-dark-gray">
                        <div class="flex items-center justify-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-navy-blue"></div>
                            <span class="ml-2">Generando resumen...</span>
                        </div>
                    </div>
                    <div class="mt-6 text-sm text-gray-500">
                        <p>Este resumen fue generado automáticamente por IA. Puede contener imprecisiones.</p>
                    </div>
                </div>
            </div>
        `;
    }

    async summarizeContent(content) {
        try {
            const response = await fetch(GEMINI_API_URL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    contents: [{ parts: [{ text: `Resume el siguiente texto de forma académica y rigurosa en español:\n\n"""${content}\n"""` }] }]
                })
            });
            const data = await response.json();
            if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts) {
                const summaryText = data.candidates[0].content.parts.map(p => p.text).join(' ');
                return this.parseSummary(summaryText);
            }
            throw new Error('No se pudo obtener el resumen.');
        } catch (error) {
            console.error('Error al generar el resumen:', error);
            throw new Error('No se pudo generar el resumen en este momento.');
        }
    }

    parseSummary(summaryText) {
        // Lógica para parsear el texto del resumen y extraer puntos principales, términos clave y conclusión
        // Por ahora, simulamos el parseo dividiendo el texto en párrafos
        const paragraphs = summaryText.split('\n').filter(p => p.trim() !== '');
        return {
            mainPoints: paragraphs.slice(0, -2),
            keyTerms: this.extractKeyTerms(paragraphs[paragraphs.length - 2]),
            conclusion: paragraphs[paragraphs.length - 1]
        };
    }

    extractKeyTerms(termsText) {
        // Lógica para extraer términos y definiciones de un texto dado
        // Por ahora, simulamos la extracción dividiendo el texto en términos separados por comas
        return termsText.split(',').map(term => {
            const [t, d] = term.split(':');
            return { term: t.trim(), definition: d ? d.trim() : '' };
        });
    }

    renderSummary(summary) {
        return `
            <div class="space-y-6">
                <div>
                    <h4 class="font-bold text-lg text-navy-blue mb-2">Puntos Principales</h4>
                    <ul class="list-disc list-inside space-y-2">
                        ${summary.mainPoints.map(point => `
                            <li>${point}</li>
                        `).join('')}
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-bold text-lg text-navy-blue mb-2">Términos Clave</h4>
                    <dl class="grid grid-cols-1 gap-4">
                        ${summary.keyTerms.map(({ term, definition }) => `
                            <div class="bg-beige p-3 rounded">
                                <dt class="font-semibold">${term}</dt>
                                <dd class="text-sm mt-1">${definition}</dd>
                            </div>
                        `).join('')}
                    </dl>
                </div>

                <div>
                    <h4 class="font-bold text-lg text-navy-blue mb-2">Conclusión</h4>
                    <p>${summary.conclusion}</p>
                </div>
            </div>
        `;
    }

    showModal() {
        const modal = document.createElement('div');
        modal.innerHTML = this.modalTemplate;
        document.body.appendChild(modal);

        const closeBtn = document.getElementById('close-modal-btn');
        closeBtn.addEventListener('click', () => {
            modal.remove();
        });

        // Cerrar modal con Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                modal.remove();
            }
        });

        return document.getElementById('summary-content');
    }
}

const GEMINI_API_KEY = 'AIzaSyDB9xnWKLzwafH_P3-Bl4z9zIGhe3wtMGU';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=' + GEMINI_API_KEY;

export function initAiSummary() {
    const summarizeBtn = document.getElementById('summarize-btn');
    if (!summarizeBtn) return;

    const aiManager = new AISummaryManager();
    
    summarizeBtn.addEventListener('click', async () => {
        const articleContent = document.querySelector('.article-content');
        if (!articleContent) return;

        const contentText = articleContent.textContent;
        const summaryContainer = aiManager.showModal();

        try {
            const summary = await aiManager.summarizeContent(contentText);
            summaryContainer.innerHTML = aiManager.renderSummary(summary);
            lucide.createIcons(); // Re-inicializar los íconos de Lucide
        } catch (error) {
            summaryContainer.innerHTML = `
                <div class="text-red-600">
                    <p>Lo sentimos, hubo un error al generar el resumen.</p>
                    <p class="text-sm mt-2">${error.message}</p>
                </div>
            `;
        }
    });
}