import { router } from './router.js';
import { initSearch } from './search.js';

window.addEventListener('DOMContentLoaded', () => {
  router();
  initSearch();
});

document.addEventListener('view:loaded', (e) => {
  // Inicializar lógica específica de cada vista
  if (window.location.pathname.startsWith('/articulo/')) {
    import('./ai.js').then(mod => mod.initArticleAI());
    import('./dictionary.js').then(mod => mod.initDictionary());
  }
});