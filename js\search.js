import { db } from './services/firebase.js';
import { collection, query, where, getDocs } from 'https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore.js';
import { mockArticles } from './services/mockdata.js';

const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

export function initSearch() {
    const searchInput = document.getElementById('search-input');
    if (!searchInput) return;

    let resultsContainer = document.getElementById('search-results-container');
    if (!resultsContainer) {
        resultsContainer = document.createElement('div');
        resultsContainer.id = 'search-results-container';
        resultsContainer.className = 'absolute mt-2 w-64 bg-white rounded-md shadow-lg z-10 hidden';
        searchInput.parentElement.style.position = 'relative';
        searchInput.parentElement.appendChild(resultsContainer);
    }

    const performSearch = async (query) => {
        if (query.length < 2) {
            resultsContainer.innerHTML = '';
            resultsContainer.classList.add('hidden');
            return;
        }

        let results = [];
        
        try {
            // Intentar búsqueda en Firebase primero
            const articlesRef = collection(db, 'articles');
            const q = query(articlesRef, 
                where('searchTerms', 'array-contains', query.toLowerCase())
            );
            const querySnapshot = await getDocs(q);
            results = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        } catch (error) {
            // Fallback a datos mock si Firebase falla
            results = mockArticles.filter(article => 
                article.title.toLowerCase().includes(query) ||
                article.summary.toLowerCase().includes(query) ||
                article.tags.some(tag => tag.toLowerCase().includes(query))
            );
        }

        if (results.length > 0) {
            resultsContainer.innerHTML = `
                <ul class="py-1">
                    ${results.map(article => `
                        <li>
                            <a href="/articulo/${article.slug}" data-link
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <span class="flex-grow">${article.title}</span>
                                ${article.tags ? `
                                    <span class="text-xs text-gray-500 ml-2">
                                        ${article.tags[0]}
                                    </span>
                                ` : ''}
                            </a>
                        </li>
                    `).join('')}
                </ul>
            `;
            resultsContainer.classList.remove('hidden');
        } else {
            resultsContainer.innerHTML = `
                <div class="px-4 py-2 text-sm text-gray-500">
                    No se encontraron resultados.
                </div>
            `;
            resultsContainer.classList.remove('hidden');
        }
    };

    const debouncedSearch = debounce((query) => {
        performSearch(query);
    }, 300);

    searchInput.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase().trim();
        debouncedSearch(query);
    });

    // Manejar navegación con teclado
    searchInput.addEventListener('keydown', (e) => {
        const results = resultsContainer.querySelectorAll('a[data-link]');
        if (!results.length) return;

        let currentIndex = Array.from(results).findIndex(el => el === document.activeElement);

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (currentIndex < 0) {
                    results[0].focus();
                } else if (currentIndex < results.length - 1) {
                    results[currentIndex + 1].focus();
                }
                break;
            case 'ArrowUp':
                e.preventDefault();
                if (currentIndex > 0) {
                    results[currentIndex - 1].focus();
                }
                break;
            case 'Escape':
                e.preventDefault();
                resultsContainer.classList.add('hidden');
                searchInput.blur();
                break;
        }
    });

    // Ocultar resultados al hacer clic fuera
    document.addEventListener('click', (e) => {
        if (!searchInput.contains(e.target) && !resultsContainer.contains(e.target)) {
            resultsContainer.classList.add('hidden');
        }
    });
}