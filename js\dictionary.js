// dictionary.js - Consultor IA y glosario interactivo
import { mockGlossary } from './services/mockdata.js';

export function initDictionary() {
  document.querySelectorAll('article .prose').forEach(container => {
    container.addEventListener('mouseup', async (e) => {
      const selection = window.getSelection();
      const selectedText = selection.toString().trim();
      if (selectedText.length > 0) {
        showDictionaryModal(selectedText);
      }
    });
  });
}

function showDictionaryModal(term) {
  let definition = mockGlossary.find(entry => entry.term.toLowerCase() === term.toLowerCase());
  const modal = document.createElement('div');
  modal.className = 'fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50';
  modal.innerHTML = `
    <div class="bg-white rounded-lg p-8 max-w-lg w-full relative">
      <button class="absolute top-2 right-2 text-gray-500 hover:text-gold-500" id="close-dict-modal">&times;</button>
      <div class="text-gray-800">
        <h3 class="font-sans font-semibold text-lg mb-2">${term}</h3>
        <div id="dict-modal-content">$${definition ? definition.definition : '<span class="text-gray-500">Buscando definición IA...</span>'}</div>
      </div>
    </div>
  `;
  document.body.appendChild(modal);
  document.getElementById('close-dict-modal').onclick = () => modal.remove();
  if (!definition) {
    getGeminiDefinition(term).then(def => {
      modal.querySelector('#dict-modal-content').innerHTML = def;
    }).catch(() => {
      modal.querySelector('#dict-modal-content').innerHTML = '<span class="text-red-600">No se pudo obtener definición IA.</span>';
    });
  }
}

async function getGeminiDefinition(term) {
  const GEMINI_API_KEY = 'AIzaSyDB9xnWKLzwafH_P3-Bl4z9zIGhe3wtMGU';
  const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=' + GEMINI_API_KEY;
  const prompt = `Explica el término teológico o bíblico "${term}" de forma académica y rigurosa en español.`;
  const response = await fetch(GEMINI_API_URL, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      contents: [{ parts: [{ text: prompt }] }]
    })
  });
  const data = await response.json();
  if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts) {
    return data.candidates[0].content.parts.map(p => p.text).join(' ');
  }
  throw new Error('No se pudo obtener la definición.');
}
