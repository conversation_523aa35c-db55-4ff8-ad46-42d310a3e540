<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Et veritas liberabit vos</title>
    <meta name="description" content="Un blog de teología, filosofía y exégesis.">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/typography.css">
    <!-- Lucide Icons -->
    <script defer src="https://unpkg.com/lucide@latest/dist/lucide.min.js"></script>
    <!-- Marked.js for Markdown Parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body class="app-shell">
  <header class="bg-navy-900 text-ivory-50 py-4 shadow">
    <div class="container mx-auto flex items-center justify-between px-4">
      <a href="/" class="flex items-center gap-2 text-2xl font-sans font-bold tracking-tight">
        <span class="inline-block"><i data-lucide="book-open"></i></span>
        Et veritas liberabit vos
      </a>
      <nav class="flex gap-6">
        <a href="/" class="hover:text-gold-500 transition-colors">Inicio</a>
        <a href="/recursos" class="hover:text-gold-500 transition-colors">Recursos</a>
        <a href="/linea-temporal" class="hover:text-gold-500 transition-colors">Línea de tiempo</a>
        <a href="/admin" class="hover:text-gold-500 transition-colors">Admin</a>
      </nav>
      <div class="relative">
        <input id="search-input" type="text" placeholder="Buscar artículos..." class="rounded-md px-3 py-1 text-gray-800 focus:outline-none focus:ring-2 focus:ring-gold-500" autocomplete="off">
      </div>
    </div>
  </header>
  <main id="app-root" class="flex-grow container mx-auto px-4 py-8"></main>
  <footer class="bg-navy-900 text-ivory-50 py-6 mt-12">
    <div class="container mx-auto text-center text-sm">
      &copy; 2025 Et veritas liberabit vos. Todos los derechos reservados.
    </div>
  </footer>
  <script type="module" src="/js/main.js"></script>
</body>
</html>