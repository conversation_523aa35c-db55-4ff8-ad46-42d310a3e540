import { initializeApp } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-app.js";
import { 
    getFirestore, 
    collection, 
    doc,
    addDoc,
    getDoc,
    getDocs,
    updateDoc,
    deleteDoc,
    query,
    where,
    orderBy,
    limit,
    serverTimestamp
} from "https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore.js";
import { 
    getAuth,
    signInWithEmailAndPassword,
    signOut,
    onAuthStateChanged 
} from "https://www.gstatic.com/firebasejs/9.6.1/firebase-auth.js";
import { 
    getStorage,
    ref,
    uploadBytes,
    getDownloadURL 
} from "https://www.gstatic.com/firebasejs/9.6.1/firebase-storage.js";

// TODO: Reemplaza esto con la configuración de tu proyecto de Firebase
const firebaseConfig = {
    apiKey: "TU_API_KEY",
    authDomain: "TU_AUTH_DOMAIN",
    projectId: "TU_PROJECT_ID",
    storageBucket: "TU_STORAGE_BUCKET",
    messagingSenderId: "TU_MESSAGING_SENDER_ID",
    appId: "TU_APP_ID"
};

// Inicializar Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);
const storage = getStorage(app);

// Helpers para Firestore
export const createArticle = async (articleData) => {
    try {
        const articlesRef = collection(db, 'articles');
        const docRef = await addDoc(articlesRef, {
            ...articleData,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            searchTerms: generateSearchTerms(articleData)
        });
        return docRef.id;
    } catch (error) {
        console.error("Error al crear el artículo:", error);
        throw error;
    }
};

export const updateArticle = async (id, articleData) => {
    try {
        const docRef = doc(db, 'articles', id);
        await updateDoc(docRef, {
            ...articleData,
            updatedAt: serverTimestamp(),
            searchTerms: generateSearchTerms(articleData)
        });
    } catch (error) {
        console.error("Error al actualizar el artículo:", error);
        throw error;
    }
};

export const deleteArticle = async (id) => {
    try {
        await deleteDoc(doc(db, 'articles', id));
    } catch (error) {
        console.error("Error al eliminar el artículo:", error);
        throw error;
    }
};

// Helper para generar términos de búsqueda
const generateSearchTerms = (articleData) => {
    const terms = new Set();
    const addTerms = (text) => {
        if (!text) return;
        text.toLowerCase()
            .split(/[\s.,;:!?()[\]{}"']+/)
            .filter(term => term.length > 2)
            .forEach(term => terms.add(term));
    };

    addTerms(articleData.title);
    addTerms(articleData.summary);
    articleData.tags?.forEach(tag => addTerms(tag));

    return Array.from(terms);
};

// Helpers para autenticación
export const loginUser = async (email, password) => {
    try {
        const userCredential = await signInWithEmailAndPassword(auth, email, password);
        return userCredential.user;
    } catch (error) {
        console.error("Error al iniciar sesión:", error);
        throw error;
    }
};

export const logoutUser = async () => {
    try {
        await signOut(auth);
    } catch (error) {
        console.error("Error al cerrar sesión:", error);
        throw error;
    }
};

export const onAuthChange = (callback) => {
    return onAuthStateChanged(auth, callback);
};

// Helpers para Storage
export const uploadFile = async (file, path) => {
    try {
        const storageRef = ref(storage, path);
        const snapshot = await uploadBytes(storageRef, file);
        return await getDownloadURL(snapshot.ref);
    } catch (error) {
        console.error("Error al subir el archivo:", error);
        throw error;
    }
};

export {
    db,
    auth,
    storage,
    collection,
    doc,
    getDoc,
    getDocs,
    query,
    where,
    orderBy,
    limit,
    serverTimestamp
};