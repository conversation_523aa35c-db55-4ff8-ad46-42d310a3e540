// import { auth } from '../services/firebase.js';
// import { onAuthStateChanged } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-auth.js";
import { mockArticles } from '../services/mockdata.js';

const AdminPanel = {
    render: async () => {
        // Simulación de estado de autenticación
        const isAuthenticated = true; // Cambiar a true para ver el panel de administrador

        if (!isAuthenticated) {
            window.location.href = '/login';
            return '';
        }

        return `
            <section>
                <h1 class="text-2xl font-sans font-bold mb-4">Panel de Administración</h1>
                <form id="article-form" class="bg-white rounded-lg shadow p-6 mb-8">
                    <div class="mb-4">
                        <label class="block font-sans mb-1">Título</label>
                        <input type="text" name="title" class="w-full border rounded px-3 py-2" required>
                    </div>
                    <div class="mb-4">
                        <label class="block font-sans mb-1">Slug</label>
                        <input type="text" name="slug" class="w-full border rounded px-3 py-2" required>
                    </div>
                    <div class="mb-4">
                        <label class="block font-sans mb-1">Resumen</label>
                        <textarea name="summary" class="w-full border rounded px-3 py-2" required></textarea>
                    </div>
                    <div class="mb-4">
                        <label class="block font-sans mb-1">Contenido (Markdown)</label>
                        <textarea name="content" class="w-full border rounded px-3 py-2" rows="8" required></textarea>
                    </div>
                    <div class="mb-4">
                        <label class="block font-sans mb-1">Etiquetas (separadas por coma)</label>
                        <input type="text" name="tags" class="w-full border rounded px-3 py-2">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        Guardar Artículo
                    </button>
                </form>
                <h2 class="text-xl font-sans font-semibold mb-2">Artículos existentes</h2>
                <ul>
                    ${mockArticles.map(article => `<li class="mb-2">${article.title} <span class="text-xs text-gray-500">(${article.slug})</span></li>`).join('')}
                </ul>
            </section>
        `;
    },
    after_render: async () => {
        const form = document.getElementById('article-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                // Lógica para guardar el artículo en Firebase
                console.log('Formulario enviado');
            });
        }
    }
};

export default AdminPanel;