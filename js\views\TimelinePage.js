import { mockTimeline } from '../services/mockdata.js';

const TimelinePage = {
    render: async () => {
        return `
            <section>
                <h1 class="text-2xl font-sans font-bold mb-6"><PERSON><PERSON><PERSON> de Tiempo</h1>
                <div class="relative border-l-2 border-gold-500 pl-8">
                    ${mockTimeline.map(event => `
                        <div class="mb-8">
                            <div class="absolute -left-4 w-8 h-8 bg-gold-500 rounded-full flex items-center justify-center text-white font-bold">${event.date}</div>
                            <div class="ml-8">
                                <h3 class="font-sans font-semibold text-lg">${event.title}</h3>
                                <p class="text-gray-700">${event.description}</p>
                                <a href="${event.link}" target="_blank" class="text-gold-500 hover:underline text-sm">Más información</a>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </section>
        `;
    },
    after_render: async () => {
        // No se necesita lógica post-renderizado para la página de cronología
    }
};

export default TimelinePage;