import { mockArticles, mockGlossary } from '../services/mockdata.js';
import { initInteractiveElements } from '../interactive.js';
import { initAiSummary } from '../ai.js';

const ArticlePage = {
    render: async (slug) => {
        // Usar datos de demostración
        const article = mockArticles.find(p => p.slug === slug);

        if (!article) {
            return `<div class="text-center text-gray-500">Artículo no encontrado.</div>`;
        }

        // Simular autor y fecha de creación
        article.author = 'Equipo Editorial';
        article.createdAt = new Date().toLocaleDateString();

        // Procesar el contenido Markdown a HTML
        const htmlContent = window.marked.parse(article.content);

        // Diccionario de términos
        const glossaryHtml = mockGlossary.length ? `
            <aside class="bg-gray-50 border-l-4 border-gold-500 p-4 mt-8">
                <h3 class="font-sans font-semibold text-lg mb-2">Glosario</h3>
                <ul>
                    ${mockGlossary.map(term => `<li><strong>${term.term}:</strong> ${term.definition}</li>`).join('')}
                </ul>
            </aside>
        ` : '';

        return `
            <article class="prose max-w-none">
                <h1 class="font-sans text-3xl font-bold mb-4">${article.title}</h1>
                <div class="mb-6 text-sm text-gray-500">${article.createdAt}</div>
                <div>${htmlContent}</div>
                <button id="ai-summary-btn" class="btn btn-primary mt-6">
                    Resumen IA
                </button>
                <div id="ai-summary-modal" class="hidden fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-8 max-w-lg w-full relative">
                        <button id="close-summary-modal" class="absolute top-2 right-2 text-gray-500 hover:text-gold-500">&times;</button>
                        <div id="ai-summary-content" class="text-gray-800"></div>
                    </div>
                </div>
                ${glossaryHtml}
            </article>
        `;
    },
    after_render: async () => {
        initInteractiveElements();
        initAiSummary();
    }
};

export default ArticlePage;