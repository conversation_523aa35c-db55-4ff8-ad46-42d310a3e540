const ResourcesPage = {
    render: async () => {
        return `
            <section>
              <h1 class="text-2xl font-sans font-bold mb-4">Recursos Académicos y Teológicos</h1>
              <ul class="list-disc pl-6 space-y-2">
                <li><a href="https://www.bibliacatolica.com.br/" target="_blank" rel="noopener" class="text-gold-500 hover:underline">Biblia Católica Online</a></li>
                <li><a href="https://www.bibliatodo.com/" target="_blank" rel="noopener" class="text-gold-500 hover:underline">Bibliatodo</a></li>
                <li><a href="https://www.earlychristianwritings.com/" target="_blank" rel="noopener" class="text-gold-500 hover:underline">Early Christian Writings</a></li>
                <li><a href="https://plato.stanford.edu/entries/christian-theology-philosophy/" target="_blank" rel="noopener" class="text-gold-500 hover:underline">Stanford Encyclopedia of Philosophy: Christian Theology</a></li>
              </ul>
            </section>
        `;
    },
    after_render: async () => {
        // No se necesita lógica post-renderizado para la página de recursos
    }
};

export default ResourcesPage;