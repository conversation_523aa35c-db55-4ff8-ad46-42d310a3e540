import { db } from './services/firebase.js';
import { collection, query, where, getDocs } from 'https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore.js';

class InteractiveElements {
    constructor(container) {
        this.container = container;
        this.glossaryCache = new Map();
        this.activeTooltip = null;
        this.tooltipDelay = 200;
        this.tooltipTimeout = null;
        this.bindGlobalEvents();
    }

    bindGlobalEvents() {
        // Cerrar tooltips al hacer scroll
        window.addEventListener('scroll', () => {
            this.hideAllTooltips();
        }, { passive: true });

        // Cerrar tooltips al presionar Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAllTooltips();
            }
        });

        // Gestionar tooltips al hacer clic fuera
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.tooltip-trigger')) {
                this.hideAllTooltips();
            }
        });
    }

    async initializeFootnotes() {
        const footnotes = this.container.querySelectorAll('fn');
        const footnotesList = document.createElement('div');
        footnotesList.className = 'mt-8 pt-4 border-t border-gray-200';
        footnotesList.innerHTML = '<h3 class="text-xl font-bold text-navy-blue mb-4">Notas al pie</h3>';

        footnotes.forEach((fn, index) => {
            const noteId = `note-${index + 1}`;
            const tooltipId = `tooltip-${index + 1}`;
            
            // Crear el marcador de nota
            const noteMarker = document.createElement('sup');
            noteMarker.id = noteId;
            noteMarker.className = 'tooltip-trigger cursor-pointer text-gold font-bold ml-1';
            noteMarker.textContent = `[${index + 1}]`;
            
            // Crear el tooltip
            const tooltip = this.createTooltip(tooltipId, fn.textContent);
            
            // Crear la entrada en la lista de notas
            const noteEntry = document.createElement('div');
            noteEntry.className = 'flex mb-4';
            noteEntry.innerHTML = `
                <sup class="text-gold font-bold mr-2">${index + 1}.</sup>
                <p class="text-dark-gray">${fn.textContent}</p>
            `;
            footnotesList.appendChild(noteEntry);

            // Reemplazar la etiqueta fn con el marcador
            fn.replaceWith(noteMarker);
            noteMarker.parentElement.appendChild(tooltip);

            this.attachTooltipEvents(noteMarker, tooltip);
        });

        if (footnotes.length > 0) {
            this.container.appendChild(footnotesList);
        }
    }

    async initializeGlossaryTerms() {
        const glossaryTerms = this.container.querySelectorAll('gl');
        
        for (const term of glossaryTerms) {
            const termText = term.textContent;
            const definition = await this.getGlossaryDefinition(termText);
            
            const termSpan = document.createElement('span');
            termSpan.className = 'tooltip-trigger cursor-pointer text-navy-blue border-b border-dotted border-navy-blue';
            termSpan.textContent = termText;
            
            const tooltip = this.createTooltip(`glossary-${termText}`, definition);
            
            term.replaceWith(termSpan);
            termSpan.parentElement.appendChild(tooltip);
            
            this.attachTooltipEvents(termSpan, tooltip);
        }
    }

    async getGlossaryDefinition(term) {
        if (this.glossaryCache.has(term)) {
            return this.glossaryCache.get(term);
        }

        try {
            const glossaryRef = collection(db, 'glossary');
            const q = query(glossaryRef, where('term', '==', term.toLowerCase()));
            const querySnapshot = await getDocs(q);
            
            if (!querySnapshot.empty) {
                const definition = querySnapshot.docs[0].data().definition;
                this.glossaryCache.set(term, definition);
                return definition;
            }
        } catch (error) {
            console.error('Error al buscar en el glosario:', error);
        }

        // Definición por defecto si no se encuentra en la base de datos
        return `Definición no encontrada para "${term}".`;
    }

    createTooltip(id, content) {
        const tooltip = document.createElement('div');
        tooltip.id = id;
        tooltip.className = 'hidden absolute z-50 w-64 p-3 text-sm bg-white border border-gray-200 rounded-lg shadow-lg';
        tooltip.innerHTML = `
            <div class="relative">
                <div class="prose prose-sm">
                    ${content}
                </div>
            </div>
        `;
        return tooltip;
    }

    attachTooltipEvents(trigger, tooltip) {
        const showTooltip = () => {
            this.hideAllTooltips();
            
            this.tooltipTimeout = setTimeout(() => {
                const rect = trigger.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                
                tooltip.style.left = `${rect.left}px`;
                tooltip.style.top = `${rect.bottom + scrollTop + 5}px`;
                
                // Ajustar posición si el tooltip se sale de la pantalla
                const tooltipRect = tooltip.getBoundingClientRect();
                if (tooltipRect.right > window.innerWidth) {
                    tooltip.style.left = `${window.innerWidth - tooltipRect.width - 10}px`;
                }
                
                tooltip.classList.remove('hidden');
                this.activeTooltip = tooltip;
            }, this.tooltipDelay);
        };

        const hideTooltip = () => {
            clearTimeout(this.tooltipTimeout);
            if (this.activeTooltip === tooltip) {
                this.tooltipTimeout = setTimeout(() => {
                    tooltip.classList.add('hidden');
                    this.activeTooltip = null;
                }, this.tooltipDelay);
            }
        };

        trigger.addEventListener('mouseenter', showTooltip);
        trigger.addEventListener('mouseleave', hideTooltip);
        tooltip.addEventListener('mouseenter', () => clearTimeout(this.tooltipTimeout));
        tooltip.addEventListener('mouseleave', hideTooltip);

        // Soporte para dispositivos táctiles
        trigger.addEventListener('click', (e) => {
            e.preventDefault();
            if (this.activeTooltip === tooltip) {
                hideTooltip();
            } else {
                showTooltip();
            }
        });
    }

    hideAllTooltips() {
        if (this.activeTooltip) {
            this.activeTooltip.classList.add('hidden');
            this.activeTooltip = null;
        }
    }
}

export function initInteractiveElements() {
    const articleContent = document.querySelector('.article-content');
    if (!articleContent) return;

    const interactive = new InteractiveElements(articleContent);
    interactive.initializeFootnotes();
    interactive.initializeGlossaryTerms();
}